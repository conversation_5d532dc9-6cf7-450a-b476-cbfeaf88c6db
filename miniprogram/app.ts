// app.ts
App<IAppOption>({
  globalData: {
    userInfo: null,
    hasLogin: false
  },

  onLaunch() {
    console.log('小程序启动')

    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 检查登录状态
    this.checkLoginStatus()

    // 微信登录获取code
    this.wxLogin()
  },

  onShow() {
    console.log('小程序显示')
  },

  onHide() {
    console.log('小程序隐藏')
  },

  // 微信登录
  wxLogin() {
    wx.login({
      success: (res) => {
        console.log('微信登录成功，code:', res.code)
        if (res.code) {
          // 发送 res.code 到后台换取 openId, sessionKey, unionId
          this.getOpenId(res.code)
        } else {
          console.log('登录失败！' + res.errMsg)
        }
      },
      fail: (err) => {
        console.log('微信登录失败:', err)
      }
    })
  },

  // 获取OpenId（这里需要后端接口支持）
  getOpenId(code: string) {
    // 这里应该调用你的后端API
    // wx.request({
    //   url: 'https://your-api.com/auth/login',
    //   method: 'POST',
    //   data: { code: code },
    //   success: (res) => {
    //     console.log('获取openId成功:', res.data)
    //     // 保存登录信息
    //     this.globalData.hasLogin = true
    //     wx.setStorageSync('openId', res.data.openId)
    //   }
    // })

    // 临时模拟登录成功
    console.log('模拟登录成功，code:', code)
    this.globalData.hasLogin = true
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo')
    const openId = wx.getStorageSync('openId')

    if (userInfo) {
      this.globalData.userInfo = userInfo
    }

    if (openId) {
      this.globalData.hasLogin = true
    }
  },

  // 获取用户信息
  getUserInfo() {
    return new Promise((resolve, reject) => {
      if (this.globalData.userInfo) {
        resolve(this.globalData.userInfo)
      } else {
        wx.getUserProfile({
          desc: '用于完善用户资料',
          success: (res) => {
            console.log('获取用户信息成功:', res)
            this.globalData.userInfo = res.userInfo
            wx.setStorageSync('userInfo', res.userInfo)
            resolve(res.userInfo)
          },
          fail: (err) => {
            console.log('获取用户信息失败:', err)
            reject(err)
          }
        })
      }
    })
  }
})