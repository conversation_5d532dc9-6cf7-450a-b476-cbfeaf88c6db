# 🏛️ 博物馆预约小程序

## 项目概述
这是一个功能完整的博物馆预约和报名微信小程序，为用户提供便捷的博物馆参观预约、活动报名、展览浏览等服务。

## ✨ 核心功能

### 📱 主要页面
1. **首页** - 博物馆介绍、热门展览、快捷预约入口
2. **我的预约** - 预约记录管理、状态筛选、预约详情
3. **个人中心** - 用户信息、统计数据、功能菜单

### 🎯 功能模块
1. **展览浏览** (`/pages/exhibition/`)
   - 展览列表和详情
   - 展览预约功能
   - 展览信息展示

2. **活动报名** (`/pages/activity/`)
   - 活动列表和详情
   - 在线报名功能
   - 报名人数统计

3. **预约系统** (`/pages/booking/`)
   - 统一预约表单
   - 支持展览、活动、导览等多种类型
   - 表单验证和提交

4. **预约管理** (`/pages/appointment/`)
   - 预约记录查看
   - 状态筛选（全部、待确认、已确认、已完成、已取消）
   - 预约取消功能

5. **用户中心** (`/pages/profile/`)
   - 微信授权登录
   - 用户统计数据
   - 功能菜单导航

## 🎨 设计特色

### 视觉设计
- **主色调**: 深蓝色 (#2c3e50) + 蓝色 (#3498db) + 红色 (#e74c3c)
- **设计风格**: 现代简约，符合博物馆文化氛围
- **卡片设计**: 圆角卡片，阴影效果，层次分明
- **渐变背景**: 用户中心采用渐变背景，提升视觉效果

### 交互体验
- **TabBar导航**: 底部三个主要功能入口
- **状态筛选**: 预约页面支持多状态筛选
- **表单验证**: 完整的表单验证机制
- **空状态设计**: 友好的空状态提示

## 📁 项目结构

```
miniprogram/
├── app.json              # 应用配置（TabBar、页面路由）
├── app.ts                # 应用逻辑（微信授权、全局数据）
├── app.wxss              # 全局样式
├── pages/
│   ├── index/            # 首页
│   │   ├── index.ts      # 博物馆信息、展览活动数据
│   │   ├── index.wxml    # 横幅、快捷预约、热门展览
│   │   └── index.wxss    # 博物馆主题样式
│   ├── exhibition/       # 展览页面
│   │   ├── exhibition.ts # 展览数据管理
│   │   ├── exhibition.wxml # 展览列表/详情
│   │   └── exhibition.wxss # 展览样式
│   ├── activity/         # 活动页面
│   │   ├── activity.ts   # 活动数据管理
│   │   ├── activity.wxml # 活动列表/详情
│   │   └── activity.wxss # 活动样式
│   ├── booking/          # 预约页面
│   │   ├── booking.ts    # 预约表单逻辑
│   │   ├── booking.wxml  # 预约表单界面
│   │   └── booking.wxss  # 表单样式
│   ├── appointment/      # 我的预约
│   │   ├── appointment.ts # 预约记录管理
│   │   ├── appointment.wxml # 预约列表界面
│   │   └── appointment.wxss # 预约记录样式
│   └── profile/          # 个人中心
│       ├── profile.ts    # 用户信息管理
│       ├── profile.wxml  # 个人中心界面
│       └── profile.wxss  # 个人中心样式
└── images/               # 图片资源目录
```

## 🔧 技术特点

### 微信小程序技术栈
- **框架**: 微信小程序原生框架
- **语言**: TypeScript + WXML + WXSS
- **组件**: 使用 glass-easel 组件框架
- **样式**: v2 样式版本，支持最新特性

### 核心技术实现
1. **微信授权**: 完整的微信登录和用户信息获取流程
2. **数据管理**: 本地存储 + 模拟API数据
3. **表单处理**: 完整的表单验证和提交机制
4. **状态管理**: 页面级状态管理，数据实时更新
5. **路由导航**: TabBar + 页面跳转的混合导航

## 🚀 快速开始

### 1. 环境准备
- 微信开发者工具
- 微信小程序开发账号

### 2. 运行项目
1. 用微信开发者工具打开项目目录
2. 配置 AppID（如果有的话）
3. 点击编译运行

### 3. 功能测试
1. **首页**: 查看博物馆信息和快捷功能
2. **展览**: 浏览展览列表，点击查看详情
3. **活动**: 查看活动信息，尝试报名
4. **预约**: 填写预约表单，提交预约
5. **我的预约**: 查看预约记录，测试筛选功能
6. **个人中心**: 测试微信授权登录

## 📝 数据说明

### 模拟数据
项目使用模拟数据进行演示，包括：
- 博物馆基本信息
- 展览数据（3个示例展览）
- 活动数据（3个示例活动）
- 预约记录（3条示例记录）

### 真实数据接入
要接入真实数据，需要：
1. 配置后端API接口
2. 修改各页面的数据加载方法
3. 实现真实的预约提交功能

## 🎯 后续扩展

### 功能扩展建议
1. **支付功能**: 集成微信支付，支持付费展览
2. **地图导航**: 集成地图，提供博物馆导航
3. **语音导览**: 添加语音导览功能
4. **社交分享**: 增强分享功能，支持朋友圈分享
5. **消息推送**: 预约提醒、活动通知等
6. **评价系统**: 参观后评价和反馈

### 技术优化
1. **性能优化**: 图片懒加载、数据缓存
2. **用户体验**: 加载状态、错误处理
3. **数据同步**: 实时数据更新
4. **离线支持**: 离线数据缓存

## 📞 联系信息
- 项目类型: 博物馆预约小程序
- 开发框架: 微信小程序原生 + TypeScript
- 设计风格: 现代简约博物馆主题
