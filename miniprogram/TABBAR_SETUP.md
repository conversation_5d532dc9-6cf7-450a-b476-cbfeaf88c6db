# TabBar 和微信授权功能设置说明

## 已完成的功能

### 1. TabBar 配置
- ✅ 在 `app.json` 中添加了 tabBar 配置
- ✅ 配置了3个tab页面：
  - 首页 (pages/index/index)
  - 我的预约 (pages/appointment/appointment)
  - 个人中心 (pages/profile/profile)

### 2. 新增页面
- ✅ 创建了"我的预约"页面 (`pages/appointment/`)
- ✅ 创建了"个人中心"页面 (`pages/profile/`)
- ✅ 每个页面包含完整的 .ts, .wxml, .wxss, .json 文件

### 3. 微信授权功能增强
- ✅ 在 `app.ts` 中增强了微信授权逻辑
- ✅ 添加了用户信息获取功能
- ✅ 添加了登录状态检查
- ✅ 支持用户信息本地存储

### 4. 首页优化
- ✅ 重新设计了首页布局
- ✅ 添加了快捷功能入口
- ✅ 优化了用户信息显示

## 需要注意的事项

### TabBar 图标
当前 tabBar 配置中使用的图标路径需要您提供实际的图标文件：
- `images/icon_home.png` 和 `images/icon_home_selected.png`
- `images/icon_appointment.png` 和 `images/icon_appointment_selected.png`
- `images/icon_profile.png` 和 `images/icon_profile_selected.png`

图标规格要求：81px * 81px，PNG格式，透明背景

### 后端接口
`app.ts` 中的 `getOpenId` 方法目前是模拟实现，您需要：
1. 提供后端API接口来处理微信登录
2. 取消注释并配置正确的API地址

### 功能扩展
- 我的预约页面：可以连接实际的预约数据API
- 个人中心页面：可以添加更多用户相关功能
- 首页：可以添加更多业务功能入口

## 使用方法

1. 确保所有图标文件已放置在 `images/` 目录下
2. 如需连接后端，请配置 `app.ts` 中的API接口
3. 编译并运行小程序
4. 测试 tabBar 切换和微信授权功能

## 文件结构
```
miniprogram/
├── app.json          # 添加了tabBar配置
├── app.ts            # 增强了微信授权功能
├── images/           # TabBar图标目录
├── pages/
│   ├── index/        # 首页（已优化）
│   ├── appointment/  # 我的预约页面（新增）
│   ├── profile/      # 个人中心页面（新增）
│   └── logs/         # 日志页面（原有）
└── utils/
```
