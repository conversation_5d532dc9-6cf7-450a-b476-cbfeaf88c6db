# TabBar 图标获取指南

## 当前状态
为了避免图标文件不存在的错误，我已经暂时移除了 tabBar 中的图标配置。现在 tabBar 只显示文字，功能完全正常。

## 如何添加图标

### 方案一：继续使用纯文字 tabBar（推荐）
当前配置已经可以正常工作，tabBar 显示纯文字，简洁美观。

### 方案二：添加 PNG 图标
如果您想要图标，请按以下步骤操作：

1. **准备图标文件**
   - 格式：PNG
   - 尺寸：81px × 81px
   - 背景：透明
   - 需要准备6个文件：
     - `icon_home.png` (首页-未选中)
     - `icon_home_selected.png` (首页-选中)
     - `icon_appointment.png` (预约-未选中)
     - `icon_appointment_selected.png` (预约-选中)
     - `icon_profile.png` (个人-未选中)
     - `icon_profile_selected.png` (个人-选中)

2. **图标获取途径**
   - 使用 iconfont (https://www.iconfont.cn/)
   - 使用 Feather Icons (https://feathericons.com/)
   - 使用 Heroicons (https://heroicons.com/)
   - 自己设计或找设计师制作

3. **放置图标文件**
   将6个PNG文件放入 `miniprogram/images/` 目录

4. **更新配置**
   修改 `app.json` 中的 tabBar 配置，添加 iconPath 和 selectedIconPath

## 推荐的图标主题
- 首页：房子、主页图标
- 我的预约：日历、时钟、预约图标
- 个人中心：用户、个人资料图标

## 注意事项
- 微信小程序 tabBar 不支持 SVG 格式
- 图标文件大小建议控制在 40KB 以内
- 选中和未选中状态的图标建议使用不同颜色区分

## 如果您有图标文件
请将图标文件放入 `miniprogram/images/` 目录，然后告诉我，我可以帮您更新 app.json 配置。
