/* appointment.wxss */
.container {
  background-color: #f8f9fa;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background-color: #2c3e50;
  padding: 40rpx 30rpx;
  color: white;
}

.title {
  font-size: 42rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.8;
}

/* 标签筛选 */
.tabs {
  background-color: #fff;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #ecf0f1;
}

.tabs-scroll {
  white-space: nowrap;
}

.tabs-list {
  display: flex;
  padding: 0 30rpx;
}

.tab-item {
  padding: 15rpx 30rpx;
  margin-right: 20rpx;
  border-radius: 25rpx;
  font-size: 26rpx;
  color: #7f8c8d;
  background-color: #f8f9fa;
  white-space: nowrap;
}

.tab-item.active {
  background-color: #3498db;
  color: #fff;
}

/* 预约列表 */
.appointment-list {
  flex: 1;
  padding: 30rpx;
}

.appointment-card {
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 30rpx 15rpx;
}

.type-badge {
  background-color: #ecf0f1;
  padding: 8rpx 20rpx;
  border-radius: 15rpx;
}

.type-text {
  font-size: 22rpx;
  color: #7f8c8d;
}

.status-badge {
  padding: 8rpx 20rpx;
  border-radius: 15rpx;
}

.status-text {
  font-size: 22rpx;
  color: #fff;
}

.card-content {
  padding: 0 30rpx 25rpx;
}

.appointment-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8rpx;
}

.appointment-subtitle {
  font-size: 26rpx;
  color: #7f8c8d;
  margin-bottom: 20rpx;
}

.appointment-details {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.detail-row {
  display: flex;
  align-items: center;
}

.detail-icon {
  font-size: 24rpx;
  margin-right: 15rpx;
  width: 30rpx;
}

.detail-text {
  font-size: 26rpx;
  color: #34495e;
}

.card-actions {
  display: flex;
  padding: 20rpx 30rpx;
  background-color: #f8f9fa;
  gap: 15rpx;
}

.action-btn {
  flex: 1;
  padding: 20rpx 0;
  border-radius: 10rpx;
  font-size: 26rpx;
  text-align: center;
}

.cancel-btn {
  background-color: #e74c3c;
  color: #fff;
}

.detail-btn {
  background-color: #3498db;
  color: #fff;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 50rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-title {
  display: block;
  font-size: 32rpx;
  color: #2c3e50;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.empty-desc {
  display: block;
  font-size: 26rpx;
  color: #7f8c8d;
  margin-bottom: 40rpx;
}

.rebook-btn {
  background-color: #e74c3c;
  color: #fff;
  padding: 25rpx 50rpx;
  border-radius: 25rpx;
  font-size: 28rpx;
}
