/* appointment.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.appointment-list {
  background-color: #fff;
  border-radius: 10rpx;
  overflow: hidden;
}

.appointment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #eee;
}

.appointment-item:last-child {
  border-bottom: none;
}

.appointment-info {
  flex: 1;
}

.appointment-title {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.appointment-date {
  font-size: 28rpx;
  color: #666;
}

.appointment-status {
  padding: 10rpx 20rpx;
  background-color: #e8f5e8;
  border-radius: 20rpx;
}

.status-text {
  font-size: 24rpx;
  color: #52c41a;
}

.empty-state {
  text-align: center;
  padding: 100rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
