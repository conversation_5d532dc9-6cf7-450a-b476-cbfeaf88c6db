<!--appointment.wxml-->
<view class="container">
  <view class="header">
    <text class="title">我的预约</text>
    <text class="subtitle">管理您的预约记录</text>
  </view>

  <!-- 状态筛选标签 -->
  <view class="tabs">
    <scroll-view class="tabs-scroll" scroll-x>
      <view class="tabs-list">
        <block wx:for="{{tabs}}" wx:key="*this">
          <view class="tab-item {{activeTab === index ? 'active' : ''}}"
                bindtap="onTabTap"
                data-index="{{index}}">
            {{item}}
          </view>
        </block>
      </view>
    </scroll-view>
  </view>

  <!-- 预约列表 -->
  <scroll-view class="appointment-list" scroll-y>
    <block wx:for="{{filteredAppointments}}" wx:key="id">
      <view class="appointment-card" bindtap="onAppointmentTap" data-id="{{item.id}}">
        <view class="card-header">
          <view class="type-badge">
            <text class="type-text">{{item.type === 'exhibition' ? '展览' : item.type === 'activity' ? '活动' : '导览'}}</text>
          </view>
          <view class="status-badge" style="background-color: {{item.statusColor}}">
            <text class="status-text">{{item.status}}</text>
          </view>
        </view>

        <view class="card-content">
          <text class="appointment-title">{{item.title}}</text>
          <text class="appointment-subtitle">{{item.subtitle}}</text>

          <view class="appointment-details">
            <view class="detail-row">
              <text class="detail-icon">📅</text>
              <text class="detail-text">{{item.date}} {{item.time}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-icon">👥</text>
              <text class="detail-text">{{item.visitors}} 人</text>
            </view>
            <view class="detail-row">
              <text class="detail-icon">🎫</text>
              <text class="detail-text">{{item.bookingCode}}</text>
            </view>
          </view>
        </view>

        <view class="card-actions">
          <button wx:if="{{item.status === '待确认' || item.status === '已确认'}}"
                  class="action-btn cancel-btn"
                  bindtap="onCancelTap"
                  data-id="{{item.id}}"
                  catchtap="true">
            取消预约
          </button>
          <button class="action-btn detail-btn">
            查看详情
          </button>
        </view>
      </view>
    </block>

    <!-- 空状态 -->
    <view wx:if="{{filteredAppointments.length === 0}}" class="empty-state">
      <image class="empty-image" src="/images/empty-appointment.png" mode="aspectFit"></image>
      <text class="empty-title">暂无预约记录</text>
      <text class="empty-desc">快去预约精彩的展览和活动吧</text>
      <button class="rebook-btn" bindtap="onRebookTap">
        立即预约
      </button>
    </view>
  </scroll-view>
</view>
