<!--appointment.wxml-->
<view class="container">
  <view class="header">
    <text class="title">我的预约</text>
  </view>
  
  <view class="appointment-list">
    <block wx:for="{{appointments}}" wx:key="id">
      <view class="appointment-item" bindtap="onAppointmentTap" data-id="{{item.id}}">
        <view class="appointment-info">
          <text class="appointment-title">{{item.title}}</text>
          <text class="appointment-date">{{item.date}} {{item.time}}</text>
        </view>
        <view class="appointment-status">
          <text class="status-text">{{item.status}}</text>
        </view>
      </view>
    </block>
    
    <view wx:if="{{appointments.length === 0}}" class="empty-state">
      <text class="empty-text">暂无预约记录</text>
    </view>
  </view>
</view>
