// appointment.ts
Page({
  data: {
    appointments: [
      {
        id: 1,
        title: "示例预约",
        date: "2024-01-15",
        time: "14:00",
        status: "已确认"
      }
    ]
  },

  onLoad() {
    console.log('我的预约页面加载')
    this.loadAppointments()
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadAppointments()
  },

  loadAppointments() {
    // 这里可以调用API获取预约数据
    // 暂时使用模拟数据
    console.log('加载预约数据')
  },

  onAppointmentTap(e: any) {
    const appointmentId = e.currentTarget.dataset.id
    console.log('点击预约:', appointmentId)
    // 可以跳转到预约详情页面
  }
})
