// appointment.ts
Page({
  data: {
    activeTab: 0,
    tabs: ['全部', '待确认', '已确认', '已完成', '已取消'],
    appointments: [
      {
        id: 1,
        type: 'exhibition',
        title: "古代文明展参观",
        subtitle: "一楼展厅A",
        date: "2024-01-20",
        time: "14:00-15:00",
        status: "已确认",
        statusColor: "#2ecc71",
        visitors: 2,
        bookingCode: "BK20240120001"
      },
      {
        id: 2,
        type: 'activity',
        title: "文物修复体验活动",
        subtitle: "文物修复室",
        date: "2024-01-25",
        time: "14:00-16:00",
        status: "待确认",
        statusColor: "#f39c12",
        visitors: 1,
        bookingCode: "BK20240125002"
      },
      {
        id: 3,
        type: 'guide',
        title: "专业导览服务",
        subtitle: "全馆导览",
        date: "2024-01-18",
        time: "10:00-11:30",
        status: "已完成",
        statusColor: "#95a5a6",
        visitors: 4,
        bookingCode: "BK20240118003"
      }
    ],
    filteredAppointments: []
  },

  onLoad() {
    console.log('我的预约页面加载')
    this.loadAppointments()
  },

  onShow() {
    this.loadAppointments()
  },

  loadAppointments() {
    // 这里可以调用API获取预约数据
    console.log('加载预约数据')
    this.filterAppointments()
  },

  onTabTap(e: any) {
    const index = e.currentTarget.dataset.index
    this.setData({
      activeTab: index
    })
    this.filterAppointments()
  },

  filterAppointments() {
    const { activeTab, appointments, tabs } = this.data
    let filtered = appointments

    if (activeTab > 0) {
      const status = tabs[activeTab]
      filtered = appointments.filter(item => item.status === status)
    }

    this.setData({
      filteredAppointments: filtered
    })
  },

  onAppointmentTap(e: any) {
    const appointmentId = e.currentTarget.dataset.id
    console.log('点击预约:', appointmentId)
    // 可以跳转到预约详情页面
    wx.showToast({
      title: '查看预约详情',
      icon: 'none'
    })
  },

  onCancelTap(e: any) {
    const appointmentId = e.currentTarget.dataset.id
    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个预约吗？',
      success: (res) => {
        if (res.confirm) {
          // 这里调用取消预约的API
          wx.showToast({
            title: '取消成功',
            icon: 'success'
          })
        }
      }
    })
  },

  onRebookTap() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  }
})
