<!--activity.wxml-->
<scroll-view class="container" scroll-y>
  <!-- 活动详情页 -->
  <view wx:if="{{currentActivity}}" class="activity-detail">
    <image class="detail-banner" src="{{currentActivity.image}}" mode="aspectFill"></image>
    
    <view class="detail-content">
      <view class="detail-header">
        <text class="detail-title">{{currentActivity.title}}</text>
        <view class="detail-status">
          <text class="status-tag">{{currentActivity.status}}</text>
        </view>
      </view>
      
      <view class="detail-info">
        <view class="info-row">
          <text class="info-label">时间：</text>
          <text class="info-value">{{currentActivity.time}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">时长：</text>
          <text class="info-value">{{currentActivity.duration}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">地点：</text>
          <text class="info-value">{{currentActivity.location}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">费用：</text>
          <text class="info-value">{{currentActivity.price}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">讲师：</text>
          <text class="info-value">{{currentActivity.instructor}}</text>
        </view>
      </view>
      
      <view class="participants-info">
        <text class="section-title">报名情况</text>
        <view class="participants-bar">
          <view class="participants-progress" style="width: {{currentActivity.currentParticipants / currentActivity.maxParticipants * 100}}%"></view>
        </view>
        <text class="participants-text">{{currentActivity.currentParticipants}}/{{currentActivity.maxParticipants}} 人</text>
      </view>
      
      <view class="detail-description">
        <text class="section-title">活动介绍</text>
        <text class="description-text">{{currentActivity.description}}</text>
      </view>
      
      <view class="detail-requirements">
        <text class="section-title">报名要求</text>
        <view class="requirements-list">
          <block wx:for="{{currentActivity.requirements}}" wx:key="*this">
            <view class="requirement-item">• {{item}}</view>
          </block>
        </view>
      </view>
      
      <view class="detail-actions">
        <button class="action-btn primary" bindtap="onSignUpTap" data-id="{{currentActivity.id}}">
          立即报名
        </button>
        <button class="action-btn secondary" bindtap="onShareTap">
          分享活动
        </button>
      </view>
    </view>
  </view>
  
  <!-- 活动列表页 -->
  <view wx:else class="activity-list">
    <view class="list-header">
      <text class="page-title">活动报名</text>
      <text class="page-subtitle">参与精彩活动</text>
    </view>
    
    <view class="activities">
      <block wx:for="{{activities}}" wx:key="id">
        <view class="activity-card" bindtap="onActivityTap" data-id="{{item.id}}">
          <image class="card-image" src="{{item.image}}" mode="aspectFill"></image>
          <view class="card-content">
            <view class="card-header">
              <text class="card-title">{{item.title}}</text>
              <view class="card-status">
                <text class="status-text">{{item.status}}</text>
              </view>
            </view>
            <text class="card-description">{{item.description}}</text>
            <view class="card-info">
              <text class="card-time">🕐 {{item.time}}</text>
              <text class="card-duration">⏱️ {{item.duration}}</text>
              <text class="card-location">📍 {{item.location}}</text>
              <text class="card-price">💰 {{item.price}}</text>
            </view>
            <view class="card-participants">
              <text class="participants-label">报名人数：</text>
              <text class="participants-count">{{item.currentParticipants}}/{{item.maxParticipants}}</text>
              <view class="participants-bar-small">
                <view class="participants-progress-small" style="width: {{item.currentParticipants / item.maxParticipants * 100}}%"></view>
              </view>
            </view>
            <view class="card-actions">
              <button class="signup-btn" bindtap="onSignUpTap" data-id="{{item.id}}" catchtap="true">
                立即报名
              </button>
            </view>
          </view>
        </view>
      </block>
    </view>
  </view>
</scroll-view>
