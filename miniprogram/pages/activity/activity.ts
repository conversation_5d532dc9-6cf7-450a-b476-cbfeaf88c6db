// activity.ts
Page({
  data: {
    activities: [
      {
        id: 1,
        title: '文物修复体验活动',
        image: '/images/activity1.jpg',
        description: '亲身体验文物修复的神奇过程，了解文物保护的重要意义。专业修复师现场指导，适合青少年参与。',
        time: '2024-01-20 14:00',
        duration: '2小时',
        location: '文物修复室',
        maxParticipants: 20,
        currentParticipants: 15,
        price: '免费',
        status: '报名中',
        requirements: ['年龄12岁以上', '需要家长陪同', '穿着舒适服装'],
        instructor: '李老师 - 高级文物修复师'
      },
      {
        id: 2,
        title: '青少年考古夏令营',
        image: '/images/activity2.jpg',
        description: '为期三天的考古体验营，包含考古知识讲座、模拟发掘、文物整理等环节。让孩子们感受考古的魅力。',
        time: '2024-02-15 09:00',
        duration: '3天',
        location: '博物馆及考古基地',
        maxParticipants: 30,
        currentParticipants: 25,
        price: '299元',
        status: '即将开始',
        requirements: ['年龄8-16岁', '身体健康', '自备午餐'],
        instructor: '张教授 - 考古学专家'
      },
      {
        id: 3,
        title: '传统手工艺制作',
        image: '/images/activity3.jpg',
        description: '学习传统陶艺、剪纸、书法等手工艺制作技巧，传承中华优秀传统文化。',
        time: '2024-03-10 10:00',
        duration: '4小时',
        location: '手工艺教室',
        maxParticipants: 25,
        currentParticipants: 8,
        price: '80元',
        status: '报名中',
        requirements: ['年龄不限', '材料费包含在内'],
        instructor: '王老师 - 民间艺术家'
      }
    ],
    currentActivity: null
  },

  onLoad(options: any) {
    console.log('活动页面加载')
    if (options.id) {
      this.loadActivityDetail(options.id)
    } else {
      this.loadActivities()
    }
  },

  onShow() {
    this.loadActivities()
  },

  loadActivities() {
    console.log('加载活动数据')
  },

  loadActivityDetail(id: string) {
    const activity = this.data.activities.find(item => item.id == id)
    if (activity) {
      this.setData({
        currentActivity: activity
      })
    }
  },

  onActivityTap(e: any) {
    const activityId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/activity/activity?id=${activityId}`
    })
  },

  onSignUpTap(e: any) {
    const activityId = e.currentTarget.dataset.id
    const activity = this.data.activities.find(item => item.id == activityId)
    
    if (activity && activity.currentParticipants >= activity.maxParticipants) {
      wx.showToast({
        title: '报名人数已满',
        icon: 'none'
      })
      return
    }

    wx.navigateTo({
      url: `/pages/booking/booking?type=activity&id=${activityId}`
    })
  },

  onShareTap() {
    wx.showShareMenu({
      withShareTicket: true
    })
  }
})
