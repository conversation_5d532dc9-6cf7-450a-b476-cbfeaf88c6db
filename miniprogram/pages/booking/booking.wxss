/* booking.wxss */
.container {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.booking-form {
  padding: 30rpx;
}

.form-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.form-title {
  display: block;
  font-size: 42rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10rpx;
}

.form-subtitle {
  font-size: 28rpx;
  color: #7f8c8d;
}

.form-section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 30rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #ecf0f1;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #34495e;
  margin-bottom: 15rpx;
  font-weight: bold;
}

.form-input {
  width: 100%;
  padding: 25rpx 20rpx;
  border: 2rpx solid #ecf0f1;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #2c3e50;
  background-color: #fff;
}

.form-input:focus {
  border-color: #3498db;
}

.picker-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 20rpx;
  border: 2rpx solid #ecf0f1;
  border-radius: 10rpx;
  background-color: #fff;
}

.picker-text {
  font-size: 28rpx;
  color: #2c3e50;
}

.picker-arrow {
  font-size: 24rpx;
  color: #95a5a6;
}

.time-slots {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.time-slot {
  padding: 20rpx 30rpx;
  border: 2rpx solid #ecf0f1;
  border-radius: 25rpx;
  font-size: 26rpx;
  color: #7f8c8d;
  background-color: #fff;
  text-align: center;
  min-width: 160rpx;
}

.time-slot.selected {
  border-color: #3498db;
  background-color: #3498db;
  color: #fff;
}

.form-textarea {
  width: 100%;
  min-height: 150rpx;
  padding: 20rpx;
  border: 2rpx solid #ecf0f1;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #2c3e50;
  background-color: #fff;
}

.form-tips {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.tips-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #e74c3c;
  margin-bottom: 20rpx;
}

.tips-text {
  display: block;
  font-size: 26rpx;
  color: #7f8c8d;
  margin-bottom: 10rpx;
  line-height: 1.5;
}

.tips-text:last-child {
  margin-bottom: 0;
}

.form-actions {
  padding: 20rpx 0;
}

.submit-btn {
  width: 100%;
  background-color: #e74c3c;
  color: #fff;
  padding: 30rpx 0;
  border-radius: 15rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 15rpx rgba(231, 76, 60, 0.3);
}
