<!--booking.wxml-->
<scroll-view class="container" scroll-y>
  <view class="booking-form">
    <view class="form-header">
      <text class="form-title">预约信息</text>
      <text class="form-subtitle">请填写准确的预约信息</text>
    </view>

    <view class="form-section">
      <text class="section-title">基本信息</text>

      <view class="form-item">
        <text class="form-label">姓名 *</text>
        <input class="form-input"
               placeholder="请输入您的姓名"
               value="{{formData.name}}"
               bindinput="onNameInput" />
      </view>

      <view class="form-item">
        <text class="form-label">手机号 *</text>
        <input class="form-input"
               placeholder="请输入手机号"
               type="number"
               value="{{formData.phone}}"
               bindinput="onPhoneInput" />
      </view>

      <view class="form-item">
        <text class="form-label">身份证号</text>
        <input class="form-input"
               placeholder="请输入身份证号（可选）"
               value="{{formData.idCard}}"
               bindinput="onIdCardInput" />
      </view>
    </view>

    <view class="form-section">
      <text class="section-title">参观信息</text>

      <view class="form-item">
        <text class="form-label">参观日期 *</text>
        <picker mode="date"
                start="{{minDate}}"
                end="{{maxDate}}"
                value="{{formData.visitDate}}"
                bindchange="onDateChange">
          <view class="picker-display">
            <text class="picker-text">{{formData.visitDate || '请选择日期'}}</text>
            <text class="picker-arrow">></text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="form-label">时间段 *</text>
        <view class="time-slots">
          <block wx:for="{{timeSlots}}" wx:key="*this">
            <view class="time-slot {{selectedTimeIndex === index ? 'selected' : ''}}"
                  bindtap="onTimeSlotTap"
                  data-index="{{index}}">
              {{item}}
            </view>
          </block>
        </view>
      </view>

      <view class="form-item">
        <text class="form-label">参观人数</text>
        <picker mode="selector"
                range="{{[1,2,3,4,5,6,7,8,9,10]}}"
                value="{{formData.visitorCount - 1}}"
                bindchange="onVisitorCountChange">
          <view class="picker-display">
            <text class="picker-text">{{formData.visitorCount}} 人</text>
            <text class="picker-arrow">></text>
          </view>
        </picker>
      </view>
    </view>

    <view class="form-section">
      <text class="section-title">特殊需求</text>

      <view class="form-item">
        <text class="form-label">备注说明</text>
        <textarea class="form-textarea"
                  placeholder="如有特殊需求请在此说明（可选）"
                  value="{{formData.specialRequests}}"
                  bindinput="onSpecialRequestsInput"
                  maxlength="200">
        </textarea>
      </view>
    </view>

    <view class="form-tips">
      <text class="tips-title">温馨提示：</text>
      <text class="tips-text">• 请提前至少1天预约</text>
      <text class="tips-text">• 参观当日请携带有效身份证件</text>
      <text class="tips-text">• 如需取消或修改，请提前联系客服</text>
      <text class="tips-text">• 客服电话：400-123-4567</text>
    </view>

    <view class="form-actions">
      <button class="submit-btn" bindtap="onSubmit">
        确认预约
      </button>
    </view>
  </view>
</scroll-view>
