// booking.ts
Page({
  data: {
    bookingType: '', // exhibition, activity, guide, education
    itemId: '',
    formData: {
      name: '',
      phone: '',
      idCard: '',
      visitDate: '',
      visitTime: '',
      visitorCount: 1,
      specialRequests: ''
    },
    timeSlots: [
      '09:00-10:00',
      '10:00-11:00',
      '11:00-12:00',
      '14:00-15:00',
      '15:00-16:00',
      '16:00-17:00'
    ],
    selectedTimeIndex: -1,
    minDate: '',
    maxDate: ''
  },

  onLoad(options: any) {
    console.log('预约页面加载', options)
    
    const today = new Date()
    const minDate = this.formatDate(today)
    const maxDate = this.formatDate(new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000)) // 30天后
    
    this.setData({
      bookingType: options.type || 'exhibition',
      itemId: options.id || '',
      minDate: minDate,
      maxDate: maxDate
    })
    
    this.setNavigationTitle()
  },

  setNavigationTitle() {
    const titles = {
      exhibition: '展览预约',
      activity: '活动报名',
      guide: '导览预约',
      education: '教育活动报名'
    }
    
    wx.setNavigationBarTitle({
      title: titles[this.data.bookingType] || '预约报名'
    })
  },

  formatDate(date: Date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  onNameInput(e: any) {
    this.setData({
      'formData.name': e.detail.value
    })
  },

  onPhoneInput(e: any) {
    this.setData({
      'formData.phone': e.detail.value
    })
  },

  onIdCardInput(e: any) {
    this.setData({
      'formData.idCard': e.detail.value
    })
  },

  onDateChange(e: any) {
    this.setData({
      'formData.visitDate': e.detail.value
    })
  },

  onTimeSlotTap(e: any) {
    const index = e.currentTarget.dataset.index
    this.setData({
      selectedTimeIndex: index,
      'formData.visitTime': this.data.timeSlots[index]
    })
  },

  onVisitorCountChange(e: any) {
    this.setData({
      'formData.visitorCount': parseInt(e.detail.value)
    })
  },

  onSpecialRequestsInput(e: any) {
    this.setData({
      'formData.specialRequests': e.detail.value
    })
  },

  validateForm() {
    const { name, phone, visitDate, visitTime } = this.data.formData
    
    if (!name.trim()) {
      wx.showToast({ title: '请输入姓名', icon: 'none' })
      return false
    }
    
    if (!phone.trim()) {
      wx.showToast({ title: '请输入手机号', icon: 'none' })
      return false
    }
    
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      wx.showToast({ title: '请输入正确的手机号', icon: 'none' })
      return false
    }
    
    if (!visitDate) {
      wx.showToast({ title: '请选择参观日期', icon: 'none' })
      return false
    }
    
    if (!visitTime) {
      wx.showToast({ title: '请选择时间段', icon: 'none' })
      return false
    }
    
    return true
  },

  onSubmit() {
    if (!this.validateForm()) {
      return
    }
    
    wx.showLoading({ title: '提交中...' })
    
    // 模拟提交
    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '预约成功',
        icon: 'success'
      })
      
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/appointment/appointment'
        })
      }, 1500)
    }, 2000)
  }
})
