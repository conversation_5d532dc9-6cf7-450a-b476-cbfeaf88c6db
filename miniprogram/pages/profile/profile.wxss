/* profile.wxss */
.container {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.user-section {
  background: linear-gradient(135deg, #2c3e50, #3498db);
  padding: 50rpx 30rpx;
  margin-bottom: 30rpx;
  color: white;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 70rpx;
  margin-right: 30rpx;
  border: 4rpx solid rgba(255,255,255,0.3);
}

.user-details {
  flex: 1;
}

.nickname {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.login-tip {
  font-size: 28rpx;
  opacity: 0.8;
}

.member-tag {
  background-color: rgba(255,255,255,0.2);
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.login-btn {
  width: 100%;
  background-color: rgba(255,255,255,0.2);
  color: #fff;
  border-radius: 15rpx;
  font-size: 32rpx;
  padding: 25rpx 0;
  border: 2rpx solid rgba(255,255,255,0.3);
}

/* 用户统计 */
.stats-section {
  display: flex;
  background-color: #fff;
  margin: 0 30rpx 30rpx;
  border-radius: 20rpx;
  padding: 40rpx 0;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.stats-item {
  flex: 1;
  text-align: center;
  position: relative;
}

.stats-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1rpx;
  height: 60rpx;
  background-color: #ecf0f1;
}

.stats-value {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #e74c3c;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 26rpx;
  color: #7f8c8d;
}

.menu-section {
  background-color: #fff;
  margin: 0 30rpx 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.menu-list {
  padding: 0;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 35rpx 30rpx;
  border-bottom: 1rpx solid #f8f9fa;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.menu-icon {
  font-size: 40rpx;
  margin-right: 25rpx;
  width: 50rpx;
  text-align: center;
}

.menu-text {
  flex: 1;
}

.menu-title {
  display: block;
  font-size: 32rpx;
  color: #2c3e50;
  font-weight: bold;
  margin-bottom: 5rpx;
}

.menu-desc {
  font-size: 24rpx;
  color: #7f8c8d;
}

.menu-arrow {
  font-size: 28rpx;
  color: #bdc3c7;
}

/* 版本信息 */
.version-info {
  text-align: center;
  padding: 40rpx 0;
}

.version-text {
  font-size: 24rpx;
  color: #95a5a6;
}
