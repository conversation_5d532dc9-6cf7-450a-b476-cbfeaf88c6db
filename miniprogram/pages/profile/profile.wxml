<!--profile.wxml-->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-section">
    <view class="user-info">
      <image class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
      <view class="user-details">
        <text class="nickname">{{userInfo.nickName}}</text>
        <text wx:if="{{!hasUserInfo}}" class="login-tip">点击授权登录</text>
        <text wx:else class="member-tag">博物馆会员</text>
      </view>
    </view>

    <button wx:if="{{!hasUserInfo && canIUseGetUserProfile}}"
            class="login-btn"
            bindtap="getUserProfile">
      授权登录
    </button>
  </view>

  <!-- 用户统计 -->
  <view wx:if="{{hasUserInfo}}" class="stats-section">
    <view class="stats-item">
      <text class="stats-value">{{userStats.visitCount}}</text>
      <text class="stats-label">参观次数</text>
    </view>
    <view class="stats-item">
      <text class="stats-value">{{userStats.appointmentCount}}</text>
      <text class="stats-label">预约次数</text>
    </view>
    <view class="stats-item">
      <text class="stats-value">{{userStats.favoriteCount}}</text>
      <text class="stats-label">收藏数量</text>
    </view>
  </view>

  <!-- 菜单列表 -->
  <view class="menu-section">
    <view class="menu-list">
      <block wx:for="{{menuItems}}" wx:key="id">
        <view class="menu-item" bindtap="onMenuItemTap" data-id="{{item.id}}">
          <view class="menu-content">
            <text class="menu-icon">{{item.icon}}</text>
            <view class="menu-text">
              <text class="menu-title">{{item.title}}</text>
              <text class="menu-desc">{{item.desc}}</text>
            </view>
          </view>
          <text class="menu-arrow">></text>
        </view>
      </block>
    </view>
  </view>

  <!-- 版本信息 -->
  <view class="version-info">
    <text class="version-text">博物馆预约小程序 v1.0.0</text>
  </view>
</view>
