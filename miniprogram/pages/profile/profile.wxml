<!--profile.wxml-->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-section">
    <view class="user-info">
      <image class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
      <view class="user-details">
        <text class="nickname">{{userInfo.nickName}}</text>
        <text wx:if="{{!hasUserInfo}}" class="login-tip">点击头像登录</text>
      </view>
    </view>
    
    <button wx:if="{{!hasUserInfo && canIUseGetUserProfile}}" 
            class="login-btn" 
            bindtap="getUserProfile">
      授权登录
    </button>
  </view>

  <!-- 菜单列表 -->
  <view class="menu-section">
    <view class="menu-list">
      <block wx:for="{{menuItems}}" wx:key="id">
        <view class="menu-item" bindtap="onMenuItemTap" data-id="{{item.id}}">
          <view class="menu-content">
            <text class="menu-icon">{{item.icon}}</text>
            <text class="menu-title">{{item.title}}</text>
          </view>
          <text class="menu-arrow">></text>
        </view>
      </block>
    </view>
  </view>
</view>
