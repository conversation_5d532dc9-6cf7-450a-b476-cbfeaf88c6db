// profile.ts
const app = getApp<IAppOption>()
const defaultAvatarUrl = 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'

Page({
  data: {
    userInfo: {
      avatarUrl: defaultAvatarUrl,
      nickName: '微信用户',
    },
    hasUserInfo: false,
    canIUseGetUserProfile: wx.canIUse('getUserProfile'),
    menuItems: [
      { id: 1, title: '我的预约', icon: '📅', desc: '查看预约记录' },
      { id: 2, title: '我的收藏', icon: '❤️', desc: '收藏的展览和活动' },
      { id: 3, title: '参观历史', icon: '📋', desc: '历史参观记录' },
      { id: 4, title: '设置', icon: '⚙️', desc: '个人设置' },
      { id: 5, title: '帮助与反馈', icon: '❓', desc: '常见问题和意见反馈' },
      { id: 6, title: '关于博物馆', icon: 'ℹ️', desc: '博物馆介绍' }
    ],
    userStats: {
      visitCount: 5,
      appointmentCount: 3,
      favoriteCount: 8
    }
  },

  onLoad() {
    console.log('个人中心页面加载')
    this.checkUserInfo()
  },

  onShow() {
    this.checkUserInfo()
  },

  checkUserInfo() {
    // 检查是否已有用户信息
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.setData({
        userInfo: userInfo,
        hasUserInfo: true
      })
    }
  },

  getUserProfile() {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        console.log('获取用户信息成功:', res)
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        })
        // 保存用户信息到本地存储
        wx.setStorageSync('userInfo', res.userInfo)
      },
      fail: (err) => {
        console.log('获取用户信息失败:', err)
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        })
      }
    })
  },

  onMenuItemTap(e: any) {
    const itemId = e.currentTarget.dataset.id
    console.log('点击菜单项:', itemId)
    
    switch(itemId) {
      case 1:
        wx.switchTab({
          url: '/pages/appointment/appointment'
        })
        break
      case 2:
        wx.showToast({
          title: '设置功能开发中',
          icon: 'none'
        })
        break
      case 3:
        wx.showToast({
          title: '帮助功能开发中',
          icon: 'none'
        })
        break
      case 4:
        wx.showToast({
          title: '关于我们功能开发中',
          icon: 'none'
        })
        break
    }
  }
})
