// exhibition.ts
Page({
  data: {
    exhibitions: [
      {
        id: 1,
        title: '古代文明展',
        image: '/images/exhibition1.jpg',
        description: '探索古代文明的辉煌历史，感受千年文化的魅力。本展览展示了从新石器时代到封建社会的珍贵文物。',
        startDate: '2024-01-01',
        endDate: '2024-03-31',
        location: '一楼展厅A',
        price: '免费',
        status: '正在展出',
        highlights: ['青铜器', '陶瓷', '书画', '玉器']
      },
      {
        id: 2,
        title: '现代艺术展',
        image: '/images/exhibition2.jpg',
        description: '当代艺术家的精品力作，展现现代艺术的多元化发展。包含绘画、雕塑、装置艺术等多种形式。',
        startDate: '2024-02-01',
        endDate: '2024-04-30',
        location: '二楼展厅B',
        price: '30元',
        status: '正在展出',
        highlights: ['油画', '雕塑', '装置艺术', '数字艺术']
      },
      {
        id: 3,
        title: '科技创新展',
        image: '/images/exhibition3.jpg',
        description: '展示人类科技发展的重要成果，从古代发明到现代科技的演进历程。',
        startDate: '2024-03-01',
        endDate: '2024-05-31',
        location: '三楼展厅C',
        price: '20元',
        status: '即将开始',
        highlights: ['古代发明', '工业革命', '信息技术', '人工智能']
      }
    ],
    currentExhibition: null
  },

  onLoad(options: any) {
    console.log('展览页面加载')
    if (options.id) {
      this.loadExhibitionDetail(options.id)
    } else {
      this.loadExhibitions()
    }
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadExhibitions()
  },

  loadExhibitions() {
    // 这里可以调用API获取展览数据
    console.log('加载展览数据')
  },

  loadExhibitionDetail(id: string) {
    const exhibition = this.data.exhibitions.find(item => item.id == id)
    if (exhibition) {
      this.setData({
        currentExhibition: exhibition
      })
    }
  },

  onExhibitionTap(e: any) {
    const exhibitionId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/exhibition/exhibition?id=${exhibitionId}`
    })
  },

  onBookingTap(e: any) {
    const exhibitionId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/booking/booking?type=exhibition&id=${exhibitionId}`
    })
  },

  onShareTap() {
    wx.showShareMenu({
      withShareTicket: true
    })
  }
})
