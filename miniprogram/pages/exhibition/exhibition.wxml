<!--exhibition.wxml-->
<scroll-view class="container" scroll-y>
  <!-- 展览详情页 -->
  <view wx:if="{{currentExhibition}}" class="exhibition-detail">
    <image class="detail-banner" src="{{currentExhibition.image}}" mode="aspectFill"></image>
    
    <view class="detail-content">
      <view class="detail-header">
        <text class="detail-title">{{currentExhibition.title}}</text>
        <view class="detail-status">
          <text class="status-tag">{{currentExhibition.status}}</text>
        </view>
      </view>
      
      <view class="detail-info">
        <view class="info-row">
          <text class="info-label">展期：</text>
          <text class="info-value">{{currentExhibition.startDate}} - {{currentExhibition.endDate}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">地点：</text>
          <text class="info-value">{{currentExhibition.location}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">票价：</text>
          <text class="info-value">{{currentExhibition.price}}</text>
        </view>
      </view>
      
      <view class="detail-description">
        <text class="section-title">展览介绍</text>
        <text class="description-text">{{currentExhibition.description}}</text>
      </view>
      
      <view class="detail-highlights">
        <text class="section-title">展览亮点</text>
        <view class="highlights-list">
          <block wx:for="{{currentExhibition.highlights}}" wx:key="*this">
            <view class="highlight-item">{{item}}</view>
          </block>
        </view>
      </view>
      
      <view class="detail-actions">
        <button class="action-btn primary" bindtap="onBookingTap" data-id="{{currentExhibition.id}}">
          立即预约
        </button>
        <button class="action-btn secondary" bindtap="onShareTap">
          分享展览
        </button>
      </view>
    </view>
  </view>
  
  <!-- 展览列表页 -->
  <view wx:else class="exhibition-list">
    <view class="list-header">
      <text class="page-title">展览信息</text>
      <text class="page-subtitle">探索精彩展览</text>
    </view>
    
    <view class="exhibitions">
      <block wx:for="{{exhibitions}}" wx:key="id">
        <view class="exhibition-card" bindtap="onExhibitionTap" data-id="{{item.id}}">
          <image class="card-image" src="{{item.image}}" mode="aspectFill"></image>
          <view class="card-content">
            <view class="card-header">
              <text class="card-title">{{item.title}}</text>
              <view class="card-status">
                <text class="status-text">{{item.status}}</text>
              </view>
            </view>
            <text class="card-description">{{item.description}}</text>
            <view class="card-info">
              <text class="card-date">{{item.startDate}} - {{item.endDate}}</text>
              <text class="card-location">{{item.location}}</text>
              <text class="card-price">{{item.price}}</text>
            </view>
            <view class="card-actions">
              <button class="book-btn" bindtap="onBookingTap" data-id="{{item.id}}" catchtap="true">
                预约参观
              </button>
            </view>
          </view>
        </view>
      </block>
    </view>
  </view>
</scroll-view>
