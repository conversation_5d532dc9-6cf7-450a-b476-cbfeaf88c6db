/* exhibition.wxss */
.container {
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 展览详情页样式 */
.exhibition-detail {
  background-color: #fff;
}

.detail-banner {
  width: 100%;
  height: 400rpx;
}

.detail-content {
  padding: 40rpx 30rpx;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.detail-title {
  font-size: 42rpx;
  font-weight: bold;
  color: #2c3e50;
  flex: 1;
}

.detail-status {
  margin-left: 20rpx;
}

.status-tag {
  background-color: #e74c3c;
  color: #fff;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.detail-info {
  background-color: #f8f9fa;
  padding: 30rpx;
  border-radius: 15rpx;
  margin-bottom: 30rpx;
}

.info-row {
  display: flex;
  margin-bottom: 15rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #7f8c8d;
  width: 120rpx;
}

.info-value {
  font-size: 28rpx;
  color: #2c3e50;
  flex: 1;
}

.detail-description {
  margin-bottom: 30rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20rpx;
}

.description-text {
  font-size: 28rpx;
  color: #34495e;
  line-height: 1.6;
}

.detail-highlights {
  margin-bottom: 40rpx;
}

.highlights-list {
  display: flex;
  flex-wrap: wrap;
}

.highlight-item {
  background-color: #3498db;
  color: #fff;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  margin-right: 15rpx;
  margin-bottom: 15rpx;
}

.detail-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  padding: 25rpx 0;
  border-radius: 15rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.action-btn.primary {
  background-color: #e74c3c;
  color: #fff;
}

.action-btn.secondary {
  background-color: #ecf0f1;
  color: #2c3e50;
}

/* 展览列表页样式 */
.exhibition-list {
  padding: 30rpx;
}

.list-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10rpx;
}

.page-subtitle {
  font-size: 28rpx;
  color: #7f8c8d;
}

.exhibitions {
  display: flex;
  flex-direction: column;
}

.exhibition-card {
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.card-image {
  width: 100%;
  height: 300rpx;
}

.card-content {
  padding: 30rpx;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  flex: 1;
}

.card-status {
  margin-left: 20rpx;
}

.status-text {
  background-color: #2ecc71;
  color: #fff;
  padding: 6rpx 15rpx;
  border-radius: 15rpx;
  font-size: 22rpx;
}

.card-description {
  font-size: 26rpx;
  color: #7f8c8d;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.card-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-bottom: 25rpx;
}

.card-date, .card-location, .card-price {
  font-size: 24rpx;
  color: #95a5a6;
}

.card-actions {
  display: flex;
  justify-content: flex-end;
}

.book-btn {
  background-color: #3498db;
  color: #fff;
  padding: 15rpx 30rpx;
  border-radius: 25rpx;
  font-size: 26rpx;
}
