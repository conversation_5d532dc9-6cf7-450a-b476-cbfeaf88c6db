<!--index.wxml-->
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 博物馆横幅 -->
    <view class="museum-banner">
      <image class="banner-image" src="/images/museum-banner.jpg" mode="aspectFill"></image>
      <view class="banner-overlay">
        <text class="museum-name">{{museumInfo.name}}</text>
        <text class="museum-desc">{{museumInfo.description}}</text>
      </view>
    </view>

    <!-- 快捷预约区域 -->
    <view class="quick-booking">
      <text class="section-title">快捷预约</text>
      <view class="booking-grid">
        <view class="booking-item" bindtap="goToExhibition">
          <view class="booking-icon">🏛️</view>
          <text class="booking-text">参观预约</text>
        </view>
        <view class="booking-item" bindtap="goToActivity">
          <view class="booking-icon">🎨</view>
          <text class="booking-text">活动报名</text>
        </view>
        <view class="booking-item" bindtap="goToGuide">
          <view class="booking-icon">👥</view>
          <text class="booking-text">导览预约</text>
        </view>
        <view class="booking-item" bindtap="goToEducation">
          <view class="booking-icon">📚</view>
          <text class="booking-text">教育活动</text>
        </view>
      </view>
    </view>

    <!-- 热门展览 -->
    <view class="hot-exhibitions">
      <view class="section-header">
        <text class="section-title">热门展览</text>
        <text class="more-link" bindtap="goToExhibition">查看更多 ></text>
      </view>
      <scroll-view class="exhibition-scroll" scroll-x>
        <view class="exhibition-list">
          <block wx:for="{{hotExhibitions}}" wx:key="id">
            <view class="exhibition-card" bindtap="onExhibitionTap" data-id="{{item.id}}">
              <image class="exhibition-image" src="{{item.image}}" mode="aspectFill"></image>
              <view class="exhibition-info">
                <text class="exhibition-title">{{item.title}}</text>
                <text class="exhibition-date">{{item.startDate}} - {{item.endDate}}</text>
                <text class="exhibition-location">{{item.location}}</text>
              </view>
            </view>
          </block>
        </view>
      </scroll-view>
    </view>

    <!-- 最新活动 -->
    <view class="latest-activities">
      <view class="section-header">
        <text class="section-title">最新活动</text>
        <text class="more-link" bindtap="goToActivity">查看更多 ></text>
      </view>
      <view class="activity-list">
        <block wx:for="{{latestActivities}}" wx:key="id">
          <view class="activity-item" bindtap="onActivityTap" data-id="{{item.id}}">
            <image class="activity-image" src="{{item.image}}" mode="aspectFill"></image>
            <view class="activity-content">
              <text class="activity-title">{{item.title}}</text>
              <text class="activity-time">{{item.time}}</text>
              <text class="activity-status">{{item.status}}</text>
            </view>
          </view>
        </block>
      </view>
    </view>

    <!-- 博物馆信息 -->
    <view class="museum-info">
      <text class="section-title">参观信息</text>
      <view class="info-grid">
        <view class="info-item">
          <text class="info-label">开放时间</text>
          <text class="info-value">{{museumInfo.openTime}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">联系电话</text>
          <text class="info-value">{{museumInfo.phone}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">地址</text>
          <text class="info-value">{{museumInfo.address}}</text>
        </view>
      </view>
    </view>
  </view>
</scroll-view>
