<!--index.wxml-->
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 欢迎区域 -->
    <view class="welcome-section">
      <text class="welcome-title">欢迎使用小程序</text>
      <text class="welcome-subtitle">{{motto}}</text>
    </view>

    <!-- 用户信息区域 -->
    <view class="userinfo">
      <block wx:if="{{canIUseNicknameComp && !hasUserInfo}}">
        <button class="avatar-wrapper" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
          <image class="avatar" src="{{userInfo.avatarUrl}}"></image>
        </button>
        <view class="nickname-wrapper">
          <text class="nickname-label">昵称</text>
          <input type="nickname" class="nickname-input" placeholder="请输入昵称" bind:change="onInputChange" />
        </view>
      </block>
      <block wx:elif="{{!hasUserInfo}}">
        <button wx:if="{{canIUseGetUserProfile}}" bindtap="getUserProfile" class="login-btn">
          获取头像昵称
        </button>
        <view wx:else class="version-tip"> 请使用2.10.4及以上版本基础库 </view>
      </block>
      <block wx:else>
        <view class="user-card">
          <image bindtap="bindViewTap" class="userinfo-avatar" src="{{userInfo.avatarUrl}}" mode="cover"></image>
          <text class="userinfo-nickname">{{userInfo.nickName}}</text>
        </view>
      </block>
    </view>

    <!-- 快捷功能区域 -->
    <view class="quick-actions">
      <text class="section-title">快捷功能</text>
      <view class="action-grid">
        <view class="action-item" bindtap="goToAppointment">
          <text class="action-icon">📅</text>
          <text class="action-text">我的预约</text>
        </view>
        <view class="action-item" bindtap="goToProfile">
          <text class="action-icon">👤</text>
          <text class="action-text">个人中心</text>
        </view>
        <view class="action-item" bindtap="viewLogs">
          <text class="action-icon">📋</text>
          <text class="action-text">查看日志</text>
        </view>
      </view>
    </view>
  </view>
</scroll-view>
