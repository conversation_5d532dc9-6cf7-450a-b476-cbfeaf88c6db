// index.ts
// 获取应用实例
const app = getApp<IAppOption>()

Page({
  data: {
    museumInfo: {
      name: '城市博物馆',
      description: '探索历史，感受文化',
      openTime: '周二至周日 9:00-17:00',
      phone: '************',
      address: '市中心文化区博物馆路1号'
    },
    hotExhibitions: [
      {
        id: 1,
        title: '古代文明展',
        image: '/images/exhibition1.jpg',
        startDate: '2024-01-01',
        endDate: '2024-03-31',
        location: '一楼展厅A'
      },
      {
        id: 2,
        title: '现代艺术展',
        image: '/images/exhibition2.jpg',
        startDate: '2024-02-01',
        endDate: '2024-04-30',
        location: '二楼展厅B'
      },
      {
        id: 3,
        title: '科技创新展',
        image: '/images/exhibition3.jpg',
        startDate: '2024-03-01',
        endDate: '2024-05-31',
        location: '三楼展厅C'
      }
    ],
    latestActivities: [
      {
        id: 1,
        title: '文物修复体验活动',
        image: '/images/activity1.jpg',
        time: '2024-01-20 14:00',
        status: '报名中'
      },
      {
        id: 2,
        title: '青少年考古夏令营',
        image: '/images/activity2.jpg',
        time: '2024-02-15 09:00',
        status: '即将开始'
      }
    ]
  },

  onLoad() {
    console.log('博物馆首页加载')
    this.loadData()
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadData()
  },

  loadData() {
    // 加载展览和活动数据
    // 这里可以调用API获取最新数据
    console.log('加载博物馆数据')
  },
  // 跳转到展览页面
  goToExhibition() {
    wx.navigateTo({
      url: '/pages/exhibition/exhibition'
    })
  },

  // 跳转到活动页面
  goToActivity() {
    wx.navigateTo({
      url: '/pages/activity/activity'
    })
  },

  // 点击展览卡片
  onExhibitionTap(e: any) {
    const exhibitionId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/exhibition/exhibition?id=${exhibitionId}`
    })
  },

  // 点击活动项
  onActivityTap(e: any) {
    const activityId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/activity/activity?id=${activityId}`
    })
  },

  // 跳转到我的预约
  goToAppointment() {
    wx.switchTab({
      url: '/pages/appointment/appointment'
    })
  },

  // 跳转到个人中心
  goToProfile() {
    wx.switchTab({
      url: '/pages/profile/profile'
    })
  },
})
