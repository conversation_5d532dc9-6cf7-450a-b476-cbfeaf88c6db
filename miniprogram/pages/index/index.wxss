/**index.wxss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 0;
  padding-bottom: 30rpx;
}

/* 博物馆横幅 */
.museum-banner {
  position: relative;
  height: 400rpx;
  margin-bottom: 30rpx;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0,0,0,0.7));
  padding: 60rpx 40rpx 40rpx;
  color: white;
}

.museum-name {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.museum-desc {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 通用样式 */
.section-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 30rpx;
}

/* 热门展览 */
.hot-exhibitions {
  background-color: #fff;
  margin: 30rpx 30rpx 30rpx;
  padding: 40rpx 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.more-link {
  font-size: 28rpx;
  color: #3498db;
}

.exhibition-scroll {
  white-space: nowrap;
}

.exhibition-list {
  display: flex;
}

.exhibition-card {
  display: inline-block;
  width: 280rpx;
  margin-right: 20rpx;
  border-radius: 15rpx;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
  flex-shrink: 0;
}

.exhibition-image {
  width: 100%;
  height: 200rpx;
}

.exhibition-info {
  padding: 20rpx;
}

.exhibition-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10rpx;
}

.exhibition-date {
  display: block;
  font-size: 24rpx;
  color: #7f8c8d;
  margin-bottom: 5rpx;
}

.exhibition-location {
  font-size: 24rpx;
  color: #95a5a6;
}

/* 最新活动 */
.latest-activities {
  background-color: #fff;
  margin: 0 30rpx 30rpx;
  padding: 40rpx 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.activity-list {
  display: flex;
  flex-direction: column;
}

.activity-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #ecf0f1;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.activity-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10rpx;
}

.activity-time {
  font-size: 26rpx;
  color: #7f8c8d;
  margin-bottom: 10rpx;
}

.activity-status {
  font-size: 24rpx;
  color: #e74c3c;
  background-color: #ffeaa7;
  padding: 5rpx 15rpx;
  border-radius: 15rpx;
  align-self: flex-start;
}

/* 博物馆信息 */
.museum-info {
  background-color: #fff;
  margin: 0 30rpx 30rpx;
  padding: 40rpx 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.info-grid {
  display: flex;
  flex-direction: column;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #ecf0f1;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #7f8c8d;
  font-weight: bold;
}

.info-value {
  font-size: 28rpx;
  color: #2c3e50;
  text-align: right;
  flex: 1;
  margin-left: 20rpx;
}
