/**index.wxss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 20rpx;
}

/* 欢迎区域 */
.welcome-section {
  background-color: #fff;
  padding: 40rpx;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  text-align: center;
}

.welcome-title {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.welcome-subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 用户信息区域 */
.userinfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  padding: 40rpx;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
}

.user-card {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.userinfo-avatar {
  overflow: hidden;
  width: 128rpx;
  height: 128rpx;
  margin: 20rpx;
  border-radius: 50%;
}

.userinfo-nickname {
  font-size: 32rpx;
  color: #333;
  margin-top: 10rpx;
}

.login-btn {
  background-color: #07c160;
  color: #fff;
  border-radius: 10rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.version-tip {
  font-size: 24rpx;
  color: #999;
}

/* 快捷功能区域 */
.quick-actions {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 10rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.action-grid {
  display: flex;
  justify-content: space-around;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 10rpx;
  background-color: #f8f9fa;
  min-width: 120rpx;
}

.action-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.action-text {
  font-size: 24rpx;
  color: #666;
}

/* 原有样式保留 */
.avatar-wrapper {
  padding: 0;
  width: 56px !important;
  border-radius: 8px;
  margin-top: 40px;
  margin-bottom: 40px;
}

.avatar {
  display: block;
  width: 56px;
  height: 56px;
}

.nickname-wrapper {
  display: flex;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  border-top: .5px solid rgba(0, 0, 0, 0.1);
  border-bottom: .5px solid rgba(0, 0, 0, 0.1);
  color: black;
}

.nickname-label {
  width: 105px;
}

.nickname-input {
  flex: 1;
}
